// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

import '../../util/extension.dart';
import 'feature_module.dart';
import 'module_registration_model.dart';
import 'module_registry_builder.dart';


/// A registry for managing feature modules.
///
/// The [ModuleRegistry] is responsible for initializing feature modules
/// in the correct order based on their dependencies. It is created using
/// the [ModuleRegistryBuilder] to ensure proper validation.
///
/// IMPORTANT: This class is intended for INTERNAL USE ONLY.
/// Host applications should NOT use this class directly.
/// Instead, use the methods provided in `init_common_package.dart`.
///
/// Direct usage of this class by host applications is not supported and may
/// break in future versions without notice.
///
/// Example (for internal use only):
/// ```dart
/// // This example is for internal use only. Host applications should use
/// // the methods provided in init_common_package.dart instead.
/// final registry = ModuleRegistry.builder(GetIt.instance)
///   .register(CoreModule(), source: 'common_package')
///   .register(NetworkModule(), source: 'common_package')
///   .build(); // Validation happens automatically here
///
/// await registry.initializeAllRegisteredModules();
/// ```
class ModuleRegistry {
  /// The [GetIt] instance used for dependency injection.
  final GetIt _getIt;

  /// The set of registered modules with their registration details.
  final Map<String, ModuleRegistrationModel> _moduleRegistrations;

  /// The set of initialized modules.
  final Set<String> _initializedModules = <String>{};

  /// The set of modules currently being initialized (used for cycle detection).
  final Set<String> _initializingModules = <String>{};

  /// Gets the set of modules currently being initialized (for testing purposes).
  @visibleForTesting
  Set<String> get initializingModules => Set<String>.from(_initializingModules);

  /// Internal constructor - should only be created via the builder.
  /// This is marked as public to allow for testing, but should not be used directly.
  ModuleRegistry(this._getIt, this._moduleRegistrations);

  /// Factory method to create a builder.
  static ModuleRegistryBuilder builder(GetIt getIt) {
    return ModuleRegistryBuilder(getIt);
  }

  /// Initializes all registered modules.
  ///
  /// This method initializes all modules that have been registered with the registry.
  /// It's useful when you want to initialize all modules at once instead of selectively
  /// initializing them.
  ///
  /// Returns a [Future] that completes when all modules have been initialized.
  Future<void> initializeAllRegisteredModules() async {
    for (final ModuleRegistrationModel registration in _moduleRegistrations.values) {
      await initializeModule(registration.module);
    }
  }

  /// Initializes specific modules by name.
  ///
  /// This method initializes only the specified modules. If a module depends on
  /// other modules, those dependencies will be initialized first.
  ///
  /// Returns a [Future] that completes when all specified modules have been initialized.
  ///
  /// Throws an [ArgumentError] if the module names list is empty.
  /// Throws an [Exception] if none of the specified modules are registered.
  Future<void> initializeSpecificModules(List<String> moduleNames) async {
    if (moduleNames.isEmpty) {
      throw ArgumentError('Module names list cannot be empty');
    }

    // Check if any of the specified modules are registered
    final List<String> validModules = moduleNames
        .where((String moduleName) => isModuleRegistered(moduleName))
        .toList();

    if (validModules.isEmpty) {
      throw Exception(
          'None of the specified modules are registered: $moduleNames');
    }

    // Initialize only specified modules
    for (final String moduleName in validModules) {
      final ModuleRegistrationModel? registration = _moduleRegistrations[moduleName];
      if (registration == null) {
        throw Exception('Module $moduleName is not registered');
      }
      await initializeModule(registration.module);
    }
  }

  /// Initializes no modules (no-op).
  ///
  /// This method does nothing and is provided for API completeness.
  /// It's useful in conditional initialization scenarios where you might
  /// want to explicitly indicate that no modules should be initialized.
  ///
  /// Returns a completed [Future].
  Future<void> initializeNoModules() async {
    // Do nothing
  }

  /// Initializes a specific module.
  ///
  /// If the module depends on other modules, those will be initialized first.
  ///
  /// Returns a [Future] that completes when the module has been initialized.
  ///
  /// This method handles circular dependencies by detecting cycles and breaking them.
  /// When a cycle is detected, the module that completes the cycle is initialized
  /// without waiting for its dependencies to be fully initialized.
  @visibleForTesting
  Future<void> initializeModule(FeatureModule module) async {
    // If the module is already initialized, return
    if (_initializedModules.contains(module.name)) {
      'ModuleRegistry'.commonLog(
        'Module ${module.name} is already initialized, skipping initialization',
      );
      return;
    }

    // Detect circular dependencies
    if (_initializingModules.contains(module.name)) {
      // We've detected a cycle - log a warning but continue
      // This should not happen if validation was successful, but we handle it gracefully
      commonLog('WARNING: Circular dependency detected involving module ${module.name}');
      return;
    }

    // Mark this module as being initialized
    _initializingModules.add(module.name);

    try {
      // Find modules that provide the dependencies for this module
      for (final Type dependency in module.dependencies) {
        final FeatureModule? dependencyModule = findModuleForDependency(dependency);
        if (dependencyModule != null && dependencyModule.name != module.name) {
          await initializeModule(dependencyModule);
        }
      }

      // Initialize the module
      await module.register(_getIt);
      _initializedModules.add(module.name);
    } finally {
      // Always remove from initializing set, even if an exception occurs
      _initializingModules.remove(module.name);
    }
  }

  /// Finds a module that provides the specified dependency.
  ///
  /// Returns the module that provides the dependency, or null if no module provides it.
  /// This method prioritizes modules that are already initialized to help resolve
  /// circular dependencies more efficiently.
  @visibleForTesting
  FeatureModule? findModuleForDependency(Type dependency) {
    // Helper function to check if a module provides the dependency
    bool providesDependency(ModuleRegistrationModel registration) {
      final List<Type> dependencies = registration.module.dependencies ?? <Type>[];
      // A module provides a dependency if it's in its dependencies list
      return dependencies.contains(dependency);
    }

    // First, check if any already initialized module provides this dependency
    for (final String moduleName in _initializedModules) {
      final ModuleRegistrationModel? registration = _moduleRegistrations[moduleName];
      if (registration != null && providesDependency(registration)) {
        return registration.module;
      }
    }

    // If no initialized module provides it, check all modules
    for (final ModuleRegistrationModel registration in _moduleRegistrations.values) {
      if (providesDependency(registration)) {
        return registration.module;
      }
    }

    // If no module provides this dependency, it might be registered directly with GetIt
    // or it might be a missing dependency
    return null;
  }

  /// Checks if a module is registered with the registry.
  ///
  /// This method checks if a module with the given [moduleName] exists
  /// in the registry, regardless of whether it has been initialized.
  ///
  /// Returns true if the module is registered, false otherwise.
  bool isModuleRegistered(String moduleName) {
    return _moduleRegistrations.containsKey(moduleName);
  }

  /// Checks if a module has been initialized.
  ///
  /// This method checks if a module with the given [moduleName] has been
  /// successfully initialized through one of the initialization methods.
  ///
  /// Returns true if the module is initialized, false otherwise.
  bool isModuleInitialized(String moduleName) {
    return _initializedModules.contains(moduleName);
  }

  /// Gets the list of all registered module names.
  ///
  /// Returns a list containing the names of all modules that have been
  /// registered with the registry, regardless of initialization status.
  List<String> get registeredModules => _moduleRegistrations.keys.toList();

  /// Gets the list of all initialized module names.
  ///
  /// Returns a list containing the names of all modules that have been
  /// successfully initialized.
  List<String> get initializedModules => _initializedModules.toList();

  /// Gets a map of module dependencies for debugging purposes.
  ///
  /// The returned map contains module names as keys and lists of dependency types as values.
  @visibleForTesting
  Map<String, List<Type>> getModuleDependencies() {
    final Map<String, List<Type>> result = <String, List<Type>>{};
    for (final MapEntry<String, ModuleRegistrationModel> entry in _moduleRegistrations.entries) {
      final List<Type> dependencies = entry.value.module.dependencies ?? <Type>[];
      // Create a new list to avoid returning a reference to the internal list
      result[entry.key] = List<Type>.from(dependencies);
    }
    return result;
  }

  /// Gets a list of all registered modules with their sources.
  ///
  /// This is useful for debugging and testing.
  @visibleForTesting
  List<Map<String, dynamic>> getModuleRegistrationInfo() {
    return _moduleRegistrations.entries.map((MapEntry<String, ModuleRegistrationModel> entry) => <String, String>{
      'name': entry.key,
      'type': entry.value.module.runtimeType.toString(),
      'source': entry.value.source,
      'registeredAt': entry.value.registeredAt.toString(),
    }).toList();
  }

  /// This method allows registering modules after the registry has been built.
  ///
  /// The [source] parameter identifies where the module comes from
  /// (e.g., 'common_package', 'host_app', 'test') to provide clearer error messages.
  ///
  /// Throws an exception if a module with the same name is already registered
  /// from a different source or with a different type.
  void registerModule(FeatureModule module, {required String source}) {
    final ModuleRegistrationModel registration = ModuleRegistrationModel(
      module: module,
      source: source,
      registeredAt: DateTime.now(),
    );

    // Check if a module with the same name is already registered
    if (_moduleRegistrations.containsKey(module.name)) {
      final ModuleRegistrationModel existing = _moduleRegistrations[module.name]!;

      // If the same module type is registered again, it's fine (will be replaced)
      if (existing.module.runtimeType == module.runtimeType) {
        _moduleRegistrations[module.name] = registration;
        return;
      }

      // Otherwise, we have a conflict
      throw Exception(
        'Module name conflict detected: "${module.name}" is already '
        'registered by ${existing.module.runtimeType} from "${existing.source}". '
        'New module ${module.runtimeType} from "$source" cannot use the same name. '
        'Each module must have a unique name across all sources.'
      );
    }

    _moduleRegistrations[module.name] = registration;
  }
}
